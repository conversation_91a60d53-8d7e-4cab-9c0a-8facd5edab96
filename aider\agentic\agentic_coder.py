"""
AgenticCoder: A wrapper that adds agentic capabilities to the existing Coder class.
"""
import asyncio
from typing import Optional, List, Dict, Any
from pydantic_ai.models import Model

from ..coders.base_coder import Coder
from .task_manager import TaskManager, Task, TaskStatus
from .agents import TaskDecomposition<PERSON><PERSON>, TaskExecutionAgent, AgentContext


class MockModel:
    """Mock model for testing/demo purposes when real models aren't available."""

    def __init__(self, name="mock-model"):
        self.name = name
        self.model_name = name


class AgenticCoder:
    """
    A wrapper around the existing Coder that adds agentic task decomposition and execution.
    
    This class maintains compatibility with the existing aider workflow while adding
    the ability to break down complex requests into manageable tasks and execute them
    with human-in-the-loop control.
    """
    
    def __init__(self, base_coder: Coder, agentic_model: Optional[Model] = None, auto_approve: bool = False):
        """
        Initialize the AgenticCoder.

        Args:
            base_coder: The existing Coder instance to wrap
            agentic_model: Optional separate model for agentic operations
            auto_approve: Whether to automatically approve tasks without human confirmation
        """
        self.base_coder = base_coder
        self.task_manager = TaskManager()
        self.agentic_enabled = True
        self.human_approval_required = not auto_approve
        
        # Use the base coder's model if no separate agentic model provided
        model = agentic_model or self._create_model_from_coder(base_coder)
        
        # Initialize agents
        self.decomposition_agent = TaskDecompositionAgent(model)
        self.execution_agent = TaskExecutionAgent(model)
        
        # Create agent context
        self.agent_context = AgentContext(
            coder=base_coder,
            io=base_coder.io,
            repo=base_coder.repo
        )
        self.agent_context.task_manager = self.task_manager
    
    def _create_model_from_coder(self, coder: Coder) -> Model:
        """Create a Pydantic AI model from the coder's model using the same provider and configuration."""
        model_name = coder.main_model.name
        model_info = coder.main_model.info

        # Extract provider from model name or model info
        provider = self._get_provider_from_model(model_name, model_info)

        try:
            if provider == "openai":
                from pydantic_ai.models.openai import OpenAIModel
                # Extract the actual model name (remove provider prefix if present)
                clean_model_name = model_name.split("/")[-1] if "/" in model_name else model_name
                return OpenAIModel(clean_model_name)

            elif provider == "anthropic":
                from pydantic_ai.models.anthropic import AnthropicModel
                # Extract the actual model name (remove provider prefix if present)
                clean_model_name = model_name.split("/")[-1] if "/" in model_name else model_name
                return AnthropicModel(clean_model_name)

            elif provider == "gemini":
                from pydantic_ai.models.gemini import GeminiModel
                # Extract the actual model name (remove provider prefix if present)
                clean_model_name = model_name.split("/")[-1] if "/" in model_name else model_name
                return GeminiModel(clean_model_name)

            elif provider == "groq":
                from pydantic_ai.models.groq import GroqModel
                # Extract the actual model name (remove provider prefix if present)
                clean_model_name = model_name.split("/")[-1] if "/" in model_name else model_name
                return GroqModel(clean_model_name)

            else:
                # For other providers or unknown providers, try OpenAI-compatible first
                self.base_coder.io.tool_warning(f"Unknown provider '{provider}' for model '{model_name}', trying OpenAI-compatible")
                from pydantic_ai.models.openai import OpenAIModel
                clean_model_name = model_name.split("/")[-1] if "/" in model_name else model_name
                return OpenAIModel(clean_model_name)

        except Exception as e:
            # If we can't create a real model (e.g., missing dependency, API key issues),
            # return a mock model for testing/demo purposes
            self.base_coder.io.tool_warning(f"Could not create Pydantic AI model for {model_name}: {e}")
            self.base_coder.io.tool_warning("Using mock model for demonstration")
            return MockModel(model_name)

    def _get_provider_from_model(self, model_name: str, model_info: dict) -> str:
        """Extract the provider from the model name or model info."""
        # First try to get provider from model info
        provider = model_info.get("litellm_provider", "").lower()
        if provider:
            return provider

        # If not in model info, extract from model name
        if "/" in model_name:
            provider = model_name.split("/")[0].lower()
            return provider

        # For models without explicit provider, use aider's logic
        from aider.models import OPENAI_MODELS, ANTHROPIC_MODELS

        if model_name in OPENAI_MODELS:
            return "openai"
        elif model_name in ANTHROPIC_MODELS:
            return "anthropic"
        else:
            # Default to openai for unknown models
            return "openai"
    
    def enable_agentic_mode(self, enabled: bool = True):
        """Enable or disable agentic mode."""
        self.agentic_enabled = enabled
    
    def set_human_approval_required(self, required: bool = True):
        """Set whether human approval is required for task execution."""
        self.human_approval_required = required
    
    async def run_agentic(self, user_message: str) -> Optional[str]:
        """
        Run the agentic workflow for a user message.
        
        This method:
        1. Decomposes the request into tasks
        2. Gets human approval for the task plan
        3. Executes tasks with optional human-in-the-loop control
        4. Returns the final result
        """
        if not self.agentic_enabled:
            # Fall back to normal coder behavior
            return self.base_coder.run(with_message=user_message)
        
        try:
            # Step 1: Decompose the request into tasks
            self.base_coder.io.tool_output("🤖 Analyzing request and creating task plan...")
            decomposition_result = await self.decomposition_agent.decompose_task(
                user_message, self.agent_context
            )
            
            # Step 2: Create tasks in the task manager
            task_ids = []
            for task_data in decomposition_result.tasks:
                task_id = self.task_manager.create_task(
                    title=task_data.get('title', ''),
                    description=task_data.get('description', ''),
                    priority=task_data.get('priority', 2),
                    dependencies=task_data.get('dependencies', []),
                    metadata=task_data.get('metadata', {})
                )
                task_ids.append(task_id)
            
            # Step 3: Show task plan to user
            self._display_task_plan(decomposition_result.reasoning)
            
            if self.human_approval_required:
                if not self._get_user_approval():
                    self.base_coder.io.tool_output("Task execution cancelled by user.")
                    return None
            
            # Step 4: Execute tasks
            return await self._execute_task_plan()
            
        except Exception as e:
            self.base_coder.io.tool_error(f"Error in agentic workflow: {str(e)}")
            # Fall back to normal coder behavior
            return self.base_coder.run(with_message=user_message)
    
    def _display_task_plan(self, reasoning: str):
        """Display the task plan to the user."""
        self.base_coder.io.tool_output("\n📋 Task Plan:")
        self.base_coder.io.tool_output(f"Reasoning: {reasoning}")
        self.base_coder.io.tool_output("\nTasks to execute:")
        
        for i, task in enumerate(self.task_manager.tasks.values(), 1):
            deps_str = f" (depends on: {', '.join(task.dependencies)})" if task.dependencies else ""
            priority_str = {1: "🔴 High", 2: "🟡 Medium", 3: "🟢 Low"}[task.priority]
            
            self.base_coder.io.tool_output(
                f"{i}. {task.title} [{priority_str}]{deps_str}\n"
                f"   {task.description}"
            )
    
    def _get_user_approval(self) -> bool:
        """Get user approval for the task plan."""
        response = self.base_coder.io.prompt_ask(
            "\nDo you want to proceed with this task plan? (y/n/m for modify):",
            default="y"
        ).lower().strip()
        
        if response in ['y', 'yes']:
            return True
        elif response in ['m', 'modify']:
            # TODO: Implement task plan modification
            self.base_coder.io.tool_output("Task plan modification not yet implemented. Proceeding with current plan.")
            return True
        else:
            return False
    
    async def _execute_task_plan(self) -> Optional[str]:
        """Execute the task plan with human-in-the-loop control."""
        results = []
        
        while not self.task_manager.is_complete():
            # Get next ready task
            next_task = self.task_manager.get_next_task()
            if not next_task:
                # No ready tasks - check if we're stuck
                pending_tasks = self.task_manager.get_tasks_by_status(TaskStatus.PENDING)
                if pending_tasks:
                    self.base_coder.io.tool_error("No tasks ready to execute. Possible dependency cycle.")
                    break
                else:
                    break
            
            # Display progress
            progress = self.task_manager.get_progress_summary()
            self.base_coder.io.tool_output(
                f"\n⚡ Executing task: {next_task.title} "
                f"({progress['completed']}/{progress['total']} completed)"
            )
            
            # Ask for permission if required
            if self.human_approval_required:
                response = self.base_coder.io.prompt_ask(
                    f"Execute task '{next_task.title}'? (y/n/s for skip):",
                    default="y"
                ).lower().strip()
                
                if response in ['n', 'no']:
                    self.base_coder.io.tool_output("Task execution stopped by user.")
                    break
                elif response in ['s', 'skip']:
                    self.task_manager.update_task_status(next_task.id, TaskStatus.CANCELLED)
                    continue
            
            # Execute the task
            try:
                self.task_manager.update_task_status(next_task.id, TaskStatus.IN_PROGRESS)
                
                execution_result = await self.execution_agent.execute_task(
                    next_task, self.agent_context
                )
                
                if execution_result.success:
                    self.task_manager.update_task_status(
                        next_task.id, TaskStatus.COMPLETED, result=execution_result.result
                    )
                    self.base_coder.io.tool_output(f"✅ Task completed: {execution_result.result}")
                    results.append(execution_result.result)
                else:
                    self.task_manager.update_task_status(
                        next_task.id, TaskStatus.FAILED, error=execution_result.error
                    )
                    self.base_coder.io.tool_error(f"❌ Task failed: {execution_result.error}")
                    
                    # Ask user how to proceed
                    response = self.base_coder.io.prompt_ask(
                        "Task failed. Continue with remaining tasks? (y/n):",
                        default="y"
                    ).lower().strip()
                    
                    if response in ['n', 'no']:
                        break
                        
            except Exception as e:
                self.task_manager.update_task_status(next_task.id, TaskStatus.FAILED, error=str(e))
                self.base_coder.io.tool_error(f"❌ Task execution error: {str(e)}")
                break
        
        # Display final summary
        self._display_execution_summary()
        
        return "\n".join(results) if results else None
    
    def _display_execution_summary(self):
        """Display a summary of task execution."""
        progress = self.task_manager.get_progress_summary()
        
        self.base_coder.io.tool_output("\n📊 Execution Summary:")
        self.base_coder.io.tool_output(f"Total tasks: {progress['total']}")
        self.base_coder.io.tool_output(f"Completed: {progress['completed']}")
        self.base_coder.io.tool_output(f"Failed: {progress['failed']}")
        self.base_coder.io.tool_output(f"Progress: {progress['progress_percent']:.1f}%")
    
    def run(self, with_message=None, preproc=True):
        """
        Main run method that can use either agentic or traditional workflow.
        """
        if with_message and self.agentic_enabled:
            # Run agentic workflow
            return asyncio.run(self.run_agentic(with_message))
        else:
            # Fall back to traditional workflow
            return self.base_coder.run(with_message=with_message, preproc=preproc)
    
    def __getattr__(self, name):
        """Delegate all other attributes to the base coder."""
        return getattr(self.base_coder, name)
