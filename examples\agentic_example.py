#!/usr/bin/env python3
"""
Example demonstrating agentic AI capabilities in aider.

This script shows how to use the agentic mode to break down complex
coding tasks into manageable subtasks and execute them with human oversight.
"""

import asyncio
import sys
from pathlib import Path

# Add the aider directory to the path so we can import aider modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from aider.agentic.task_manager import TaskManager, Task, TaskStatus
from aider.agentic.agentic_coder import AgenticCoder
from aider.agentic.agents import AgentContext, TaskDecompositionAgent, TaskExecutionAgent
from aider.io import InputOutput


class MockCoder:
    """Mock coder for demonstration purposes."""
    
    def __init__(self):
        self.io = InputOutput(pretty=True, yes=False)
        self.repo = None
        self.root = str(Path.cwd())
        self.commands = None
        
    def run(self, with_message=None, preproc=True):
        """Mock run method."""
        self.io.tool_output(f"Mock coder executing: {with_message}")
        return f"Executed: {with_message}"


class MockModel:
    """Mock model for demonstration purposes."""
    
    def __init__(self, name="mock-model"):
        self.name = name


async def demonstrate_task_manager():
    """Demonstrate the TaskManager functionality."""
    print("=== Task Manager Demo ===")
    
    manager = TaskManager()
    
    # Create some tasks with dependencies
    task1_id = manager.create_task(
        title="Setup project structure",
        description="Create the basic directory structure and files",
        priority=1
    )
    
    task2_id = manager.create_task(
        title="Implement core functionality",
        description="Write the main business logic",
        priority=1,
        dependencies=[task1_id]
    )
    
    task3_id = manager.create_task(
        title="Add tests",
        description="Write comprehensive tests for the functionality",
        priority=2,
        dependencies=[task2_id]
    )
    
    task4_id = manager.create_task(
        title="Update documentation",
        description="Update README and add docstrings",
        priority=3,
        dependencies=[task2_id]
    )
    
    print(f"Created {len(manager.tasks)} tasks")
    
    # Show initial progress
    progress = manager.get_progress_summary()
    print(f"Initial progress: {progress['completed']}/{progress['total']} completed")
    
    # Simulate task execution
    print("\nExecuting tasks...")
    
    while not manager.is_complete():
        ready_tasks = manager.get_ready_tasks()
        if not ready_tasks:
            break
            
        next_task = ready_tasks[0]
        print(f"Executing: {next_task.title}")
        
        # Simulate task execution
        manager.update_task_status(next_task.id, TaskStatus.IN_PROGRESS)
        await asyncio.sleep(0.1)  # Simulate work
        manager.update_task_status(next_task.id, TaskStatus.COMPLETED, 
                                 result=f"Completed {next_task.title}")
        
        progress = manager.get_progress_summary()
        print(f"Progress: {progress['completed']}/{progress['total']} completed")
    
    print("All tasks completed!")


async def demonstrate_agentic_coder():
    """Demonstrate the AgenticCoder functionality."""
    print("\n=== Agentic Coder Demo ===")

    # Create a mock coder
    mock_coder = MockCoder()

    # Create agentic coder with auto-approve for demo
    # Use a mock model to avoid API key requirements
    mock_model = MockModel()
    agentic_coder = AgenticCoder(mock_coder, agentic_model=mock_model, auto_approve=True)
    
    print("Created AgenticCoder with auto-approve enabled")
    print(f"Human approval required: {agentic_coder.human_approval_required}")
    
    # Demonstrate task creation
    user_request = "Create a Python calculator with basic operations"
    
    print(f"\nUser request: {user_request}")
    
    # Manually create some tasks to simulate decomposition
    task_manager = agentic_coder.task_manager
    
    task1_id = task_manager.create_task(
        title="Create calculator.py file",
        description="Create the main calculator module with basic structure",
        priority=1
    )
    
    task2_id = task_manager.create_task(
        title="Implement basic operations",
        description="Add functions for add, subtract, multiply, divide",
        priority=1,
        dependencies=[task1_id]
    )
    
    task3_id = task_manager.create_task(
        title="Add input validation",
        description="Add error handling for invalid inputs",
        priority=2,
        dependencies=[task2_id]
    )
    
    task4_id = task_manager.create_task(
        title="Create main interface",
        description="Add a simple command-line interface",
        priority=2,
        dependencies=[task2_id]
    )
    
    print(f"Created {len(task_manager.tasks)} tasks for the request")
    
    # Display task plan
    print("\nTask Plan:")
    for i, task in enumerate(task_manager.tasks.values(), 1):
        deps_str = f" (depends on: {', '.join(task.dependencies)})" if task.dependencies else ""
        priority_str = {1: "🔴 High", 2: "🟡 Medium", 3: "🟢 Low"}[task.priority]
        print(f"{i}. {task.title} [{priority_str}]{deps_str}")
        print(f"   {task.description}")
    
    # Simulate execution
    print("\nSimulating task execution...")
    
    while not task_manager.is_complete():
        ready_tasks = task_manager.get_ready_tasks()
        if not ready_tasks:
            break
            
        next_task = ready_tasks[0]
        print(f"\n⚡ Executing: {next_task.title}")
        
        # Simulate task execution
        task_manager.update_task_status(next_task.id, TaskStatus.IN_PROGRESS)
        
        # Mock execution result
        result = f"Successfully completed: {next_task.title}"
        task_manager.update_task_status(next_task.id, TaskStatus.COMPLETED, result=result)
        
        print(f"✅ {result}")
        
        progress = task_manager.get_progress_summary()
        print(f"Progress: {progress['completed']}/{progress['total']} tasks completed")
    
    print("\n🎉 All tasks completed successfully!")


def demonstrate_configuration():
    """Demonstrate configuration options."""
    print("\n=== Configuration Demo ===")
    
    print("Agentic mode can be enabled with command-line arguments:")
    print("  --agentic                    Enable agentic mode")
    print("  --agentic-model MODEL        Use separate model for agentic operations")
    print("  --agentic-auto-approve       Auto-approve tasks without confirmation")
    print()
    print("Example usage:")
    print("  aider --agentic --agentic-auto-approve file.py")
    print("  aider --agentic --agentic-model gpt-4 --message 'Add error handling'")


async def main():
    """Run all demonstrations."""
    print("🤖 Aider Agentic AI Integration Demo")
    print("=" * 50)
    
    try:
        await demonstrate_task_manager()
        await demonstrate_agentic_coder()
        demonstrate_configuration()
        
        print("\n✨ Demo completed successfully!")
        print("\nTo use agentic mode in aider:")
        print("1. Install pydantic-ai: pip install pydantic-ai")
        print("2. Run aider with --agentic flag")
        print("3. Give complex coding requests and watch them get broken down into tasks")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
